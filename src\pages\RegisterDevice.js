import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { requestForToken } from '../firebase';

const RegisterDevice = () => {
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState({ type: '', message: '' });
  const [isLoading, setIsLoading] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState({});
  const [fcmToken, setFcmToken] = useState('');
  const sessionId = searchParams.get('session');

  useEffect(() => {
    // Collect device information
    const info = {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      screenResolution: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      cookieEnabled: navigator.cookieEnabled,
      onlineStatus: navigator.onLine
    };
    setDeviceInfo(info);

    // Auto-start registration if session ID is present
    if (sessionId) {
      setStatus({
        type: 'info',
        message: 'QR Code detected! Click "Register Device" to enable notifications.'
      });
    } else {
      setStatus({
        type: 'error',
        message: 'Invalid QR code. Please scan a valid registration QR code.'
      });
    }
  }, [sessionId]);

  const handleDeviceRegistration = async () => {
    if (!sessionId) {
      setStatus({
        type: 'error',
        message: 'No session ID found. Please scan a valid QR code.'
      });
      return;
    }

    setIsLoading(true);
    setStatus({ type: 'info', message: 'Requesting notification permission...' });

    try {
      // Request FCM token
      const result = await requestForToken();
      
      if (result.success) {
        setFcmToken(result.token);
        
        // Prepare registration data
        const registrationData = {
          sessionId: sessionId,
          token: result.token,
          deviceInfo: deviceInfo,
          timestamp: new Date().toISOString()
        };

        // Store in localStorage for the QR generator to pick up
        localStorage.setItem(`fcm_token_${sessionId}`, JSON.stringify(registrationData));

        // Also send directly to API
        await sendToAPI(registrationData);

        setStatus({
          type: 'success',
          message: '🎉 Device registered successfully! You will now receive push notifications.'
        });

        // Show success animation or redirect after delay
        setTimeout(() => {
          setStatus({
            type: 'success',
            message: '✅ Registration complete. You can close this page.'
          });
        }, 3000);

      } else {
        setStatus({
          type: 'error',
          message: result.error || 'Failed to get notification permission.'
        });
      }
    } catch (error) {
      setStatus({
        type: 'error',
        message: `Registration failed: ${error.message}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  const sendToAPI = async (registrationData) => {
    try {
      // Replace with your actual API endpoint
      const response = await fetch('/api/register-device', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationData)
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Device registered with API:', result);
      
    } catch (error) {
      console.error('❌ API registration failed:', error);
      // Don't fail the whole process if API fails
    }
  };

  const testNotification = () => {
    if (fcmToken) {
      // Show a test notification
      if (Notification.permission === 'granted') {
        new Notification('Test Notification', {
          body: 'Your device is successfully registered! 🎉',
          icon: '/favicon.ico'
        });
      }
    }
  };

  return (
    <div className="register-container">
      <div className="register-card">
        <div className="register-header">
          <h1>📱 Device Registration</h1>
          <p>Register this device for push notifications</p>
        </div>

        {status.message && (
          <div className={`status ${status.type}`}>
            {status.message}
          </div>
        )}

        <div className="device-info">
          <h3>📋 Device Information</h3>
          <div className="info-grid">
            <div className="info-item">
              <strong>Platform:</strong> {deviceInfo.platform}
            </div>
            <div className="info-item">
              <strong>Browser:</strong> {deviceInfo.userAgent?.split(' ')[0]}
            </div>
            <div className="info-item">
              <strong>Language:</strong> {deviceInfo.language}
            </div>
            <div className="info-item">
              <strong>Screen:</strong> {deviceInfo.screenResolution}
            </div>
            <div className="info-item">
              <strong>Timezone:</strong> {deviceInfo.timezone}
            </div>
            <div className="info-item">
              <strong>Session ID:</strong> {sessionId?.slice(0, 8)}...
            </div>
          </div>
        </div>

        <div className="register-actions">
          <button 
            className="register-button primary"
            onClick={handleDeviceRegistration}
            disabled={isLoading || !sessionId}
          >
            {isLoading ? '⏳ Registering...' : '📱 Register Device'}
          </button>

          {fcmToken && (
            <button 
              className="register-button secondary"
              onClick={testNotification}
            >
              🔔 Test Notification
            </button>
          )}
        </div>

        {fcmToken && (
          <div className="token-display">
            <h4>🔑 FCM Token Generated</h4>
            <div className="token-text">
              {fcmToken}
            </div>
            <button 
              className="copy-button"
              onClick={() => navigator.clipboard.writeText(fcmToken)}
            >
              📋 Copy Token
            </button>
          </div>
        )}

        <div className="help-section">
          <h4>❓ How it works</h4>
          <ol>
            <li>You scanned a QR code to reach this page</li>
            <li>Click "Register Device" to enable notifications</li>
            <li>Your device will be automatically registered</li>
            <li>You'll receive push notifications on this device</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default RegisterDevice;

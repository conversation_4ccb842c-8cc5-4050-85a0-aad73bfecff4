body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.app-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  text-align: center;
}

.card {
  background: white;
  border-radius: 15px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 100%;
}

.title {
  color: #333;
  margin-bottom: 30px;
  font-size: 24px;
  font-weight: 600;
}

.notification-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  margin: 10px;
}

.notification-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.notification-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.status {
  margin: 20px 0;
  padding: 15px;
  border-radius: 10px;
  font-weight: 500;
}

.status.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.token-display {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
  max-height: 100px;
  overflow-y: auto;
}

.notification-log {
  margin-top: 20px;
  text-align: left;
}

.notification-item {
  background: #f8f9fa;
  border-left: 4px solid #667eea;
  padding: 10px;
  margin: 10px 0;
  border-radius: 4px;
}

.notification-item h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.notification-item p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
}

.tab-button {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 10px 20px;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.tab-button:hover {
  background: #e9ecef;
}

.tab-button.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
}

.tab-content {
  min-height: 400px;
}

/* QR Code Generator */
.qr-generator-container {
  text-align: center;
}

.qr-section {
  margin-bottom: 30px;
}

.qr-code-wrapper {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  display: inline-block;
}

.qr-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
  margin: 20px 0;
}

.qr-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.qr-button:hover {
  background: #5a6fd8;
}

.qr-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin: 15px 0;
  text-align: left;
}

.registered-tokens {
  margin-top: 30px;
  text-align: left;
}

.token-item {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin: 10px 0;
}

.token-info p {
  margin: 5px 0;
  font-size: 14px;
}

/* Barcode Scanner */
.barcode-scanner {
  text-align: center;
}

.scanner-header {
  margin-bottom: 20px;
}

.no-camera {
  background: #f8d7da;
  color: #721c24;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

.scanner-container {
  margin: 20px 0;
}

.video-container {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}

.scanner-video {
  border: 2px solid #667eea;
  border-radius: 8px;
}

.scanning-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 8px;
}

.scan-line {
  width: 80%;
  height: 2px;
  background: #00ff00;
  animation: scan 2s linear infinite;
}

@keyframes scan {
  0% { transform: translateY(-100px); }
  100% { transform: translateY(100px); }
}

.scanner-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin: 15px 0;
}

.scanner-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: transform 0.2s;
}

.scanner-button:hover {
  transform: translateY(-1px);
}

.scanner-button.primary {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.camera-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
}

.file-upload {
  margin: 20px 0;
}

.upload-label {
  display: inline-block;
  background: #6c757d;
  color: white;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s;
}

.upload-label:hover {
  background: #5a6268;
}

.file-input {
  display: none;
}

.scan-result {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  text-align: left;
}

.result-content {
  word-break: break-all;
}

.result-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.result-button {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.result-button:hover {
  background: #218838;
}

.scanner-help {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  text-align: left;
}

.scanner-help ul {
  margin: 10px 0;
  padding-left: 20px;
}

.scanner-help li {
  margin: 5px 0;
  font-size: 14px;
}

/* Register Device Page */
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.register-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 100%;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.register-header p {
  color: #666;
  margin: 0;
}

.device-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.device-info h3 {
  margin-top: 0;
  color: #333;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.info-item {
  font-size: 14px;
  padding: 5px 0;
}

.info-item strong {
  color: #333;
}

.register-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin: 30px 0;
  flex-wrap: wrap;
}

.register-button {
  border: none;
  padding: 15px 25px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 16px;
}

.register-button.primary {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.register-button.secondary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.register-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.register-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.token-display {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
}

.token-display h4 {
  margin-top: 0;
  color: #333;
}

.token-text {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
  margin: 10px 0;
}

.copy-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.copy-button:hover {
  background: #5a6268;
}

.help-section {
  background: #e7f3ff;
  border: 1px solid #b8daff;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
}

.help-section h4 {
  margin-top: 0;
  color: #004085;
}

.help-section ol {
  margin: 10px 0;
  padding-left: 20px;
}

.help-section li {
  margin: 5px 0;
  color: #004085;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    margin: 10px;
    padding: 20px;
  }

  .tab-navigation {
    flex-direction: column;
  }

  .tab-button {
    border-radius: 8px;
  }

  .qr-actions {
    flex-direction: column;
    align-items: center;
  }

  .scanner-controls {
    flex-direction: column;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .register-actions {
    flex-direction: column;
  }
}

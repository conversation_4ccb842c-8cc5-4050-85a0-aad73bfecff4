# 🔧 Setup Guide - Get Your VAPID Key

Your Firebase configuration has been updated! Now you need to get your VAPID key to complete the setup.

## ✅ Already Configured
- ✅ Firebase Config (apiKey, projectId, etc.) - **DONE**
- ⏳ VAPID Key - **NEED TO GET THIS**

## 🔑 Get Your VAPID Key

### Step 1: Go to Firebase Console
1. Open [Firebase Console](https://console.firebase.google.com/)
2. Select your project: **push-demo-ff79a**

### Step 2: Navigate to Cloud Messaging
1. Click on **Project Settings** (gear icon)
2. Go to **Cloud Messaging** tab
3. Scroll down to **Web configuration** section

### Step 3: Generate VAPID Key
1. In the **Web Push certificates** section
2. Click **Generate key pair** button
3. Copy the **Key pair** value (this is your VAPID key)

### Step 4: Update Your Code
Replace `YOUR_PUBLIC_VAPID_KEY` in `src/firebase.js`:

```javascript
// Replace this line:
const VAPID_KEY = "YOUR_PUBLIC_VAPID_KEY";

// With your actual VAPID key:
const VAPID_KEY = "BF4f62n3...your-actual-vapid-key-here...";
```

## 🚀 Test Your Setup

After updating the VAPID key:

1. **Start the app:**
   ```bash
   npm start
   ```

2. **Open in browser:**
   - Go to http://localhost:3000
   - Click "Enable Notifications"
   - You should see a permission dialog

3. **Check for token:**
   - If successful, you'll see an FCM token
   - Copy this token for backend testing

## 🔍 Troubleshooting

### "No registration token available"
- Make sure you've added the correct VAPID key
- Ensure you're running on localhost or HTTPS
- Check browser console for errors

### Permission denied
- Check browser notification settings
- Try in incognito mode
- Make sure notifications aren't blocked for localhost

### Service worker issues
- Check if `firebase-messaging-sw.js` is accessible at `/firebase-messaging-sw.js`
- Open browser DevTools > Application > Service Workers
- Look for registration errors

## 📱 Mobile Testing

For mobile testing, you'll need HTTPS:

### Option 1: Deploy to hosting
```bash
npm run build
# Deploy to Netlify, Vercel, Firebase Hosting, etc.
```

### Option 2: Use ngrok for local HTTPS
```bash
# Install ngrok
npm install -g ngrok

# In one terminal:
npm start

# In another terminal:
ngrok http 3000
```

Then open the ngrok HTTPS URL on your mobile device.

## 🎯 What's Next?

Once you have notifications working:

1. **Set up backend** - Use examples in `backend-examples/`
2. **Send test notifications** - Use your FCM token
3. **Customize UI** - Modify styles and components
4. **Deploy to production** - Host on HTTPS for mobile users

## 📞 Need Help?

If you run into issues:
1. Check the browser console for errors
2. Verify all configuration values
3. Test in different browsers
4. Make sure you're on HTTPS for mobile testing

Your project ID: **push-demo-ff79a**
Your sender ID: **224185107862**

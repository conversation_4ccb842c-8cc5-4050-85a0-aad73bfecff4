# 🔔 React Push Notifications with QR Code Registration

A complete React app that implements push notifications using Firebase Cloud Messaging (FCM) with QR code-based device registration. Users can scan QR codes to automatically register their devices for push notifications.

## ✨ Features

- 📱 **Mobile & Desktop Support** - Works on all modern browsers
- 🔔 **Foreground Notifications** - Receive notifications when app is open
- 📲 **Background Notifications** - Receive notifications when app is closed
- 📱 **QR Code Generation** - Generate QR codes for device registration
- 📷 **QR Code Scanning** - Scan QR codes with camera or upload images
- 🎯 **Auto Registration** - Automatic FCM token generation when QR is scanned
- 📋 **Token Management** - Easy token copying for backend integration
- 📊 **Device Management** - View and manage registered devices
- 🎨 **Beautiful UI** - Modern, responsive design with tabbed interface

## 🚀 Quick Start

### 1. Firebase Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select existing one
3. Go to **Project Settings** → **General** → **Your Apps** → **Web** → **Add App**
4. Copy the Firebase configuration object
5. Go to **Project Settings** → **Cloud Messaging**:
   - Copy the **Server Key** (for backend)
   - Generate **Web Push certificates** → Copy **Public VAPID Key**

### 2. Install Dependencies

```bash
npm install
```

### 3. Configure the App

The Firebase configuration is already set up with your project details:
- ✅ **Firebase Config** - Already configured in `src/firebase.js` and `public/firebase-messaging-sw.js`
- ✅ **VAPID Key** - Already added to the configuration

### 4. Run the App

```bash
npm start
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📱 How the QR Code Flow Works

### For Administrators (QR Code Generation):
1. **Open the app** → Go to "Generate QR" tab
2. **Generate QR Code** → Creates a unique registration URL
3. **Share QR Code** → Users scan this to register their devices
4. **Monitor Registrations** → See devices as they register in real-time

### For Users (Device Registration):
1. **Scan QR Code** → Using phone camera or QR scanner app
2. **Auto-redirect** → Opens registration page in browser
3. **Click "Register Device"** → Grants notification permission
4. **FCM Token Generated** → Device is automatically registered
5. **Receive Notifications** → Start getting push notifications!

### Backend Integration:
1. **Token Collection** → FCM tokens are sent to your API automatically
2. **Device Management** → Track and manage all registered devices
3. **Send Notifications** → Use tokens to send targeted notifications

## 📱 Testing on Mobile

1. **Deploy to HTTPS** (required for push notifications):
   ```bash
   npm run build
   # Deploy to Netlify, Vercel, or any HTTPS hosting
   ```

2. **Or use ngrok for local testing**:
   ```bash
   npm install -g ngrok
   npm start
   # In another terminal:
   ngrok http 3000
   ```

3. Open the ngrok HTTPS URL on your mobile device
4. Click "Enable Notifications"
5. Copy the FCM token for backend testing

## 🔧 Backend Integration

### Node.js Example

```javascript
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const serviceAccount = require('./path/to/serviceAccountKey.json');
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// Send notification
async function sendNotification(token, title, body, data = {}) {
  const message = {
    notification: {
      title: title,
      body: body
    },
    data: data,
    token: token
  };

  try {
    const response = await admin.messaging().send(message);
    console.log('✅ Successfully sent message:', response);
    return response;
  } catch (error) {
    console.error('❌ Error sending message:', error);
    throw error;
  }
}

// Usage
sendNotification(
  'user-fcm-token-here',
  'Hello from Backend!',
  'This is a test notification 🚀',
  { customData: 'value' }
);
```

### cURL Example

```bash
curl -X POST "https://fcm.googleapis.com/fcm/send" \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
        "to": "USER_FCM_TOKEN",
        "notification": {
          "title": "Hello from cURL",
          "body": "This is a test push notification 🚀",
          "icon": "/favicon.ico"
        },
        "data": {
          "customKey": "customValue"
        }
      }'
```

### Python Example

```python
import requests
import json

def send_notification(server_key, token, title, body, data=None):
    url = "https://fcm.googleapis.com/fcm/send"
    
    headers = {
        "Authorization": f"key={server_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "to": token,
        "notification": {
            "title": title,
            "body": body,
            "icon": "/favicon.ico"
        }
    }
    
    if data:
        payload["data"] = data
    
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    return response.json()

# Usage
result = send_notification(
    server_key="YOUR_SERVER_KEY",
    token="USER_FCM_TOKEN",
    title="Hello from Python!",
    body="This is a test notification 🐍",
    data={"customKey": "customValue"}
)
print(result)
```

## 🔍 How It Works

1. **Permission Request**: App requests notification permission from browser
2. **Token Generation**: Firebase generates unique FCM token for the device/browser
3. **Token Storage**: Send token to your backend for future notifications
4. **Send Notification**: Backend sends notification via FCM API
5. **Delivery**: 
   - **Foreground**: App receives via `onMessage` listener
   - **Background**: Service worker receives via `onBackgroundMessage`

## 🛠️ Project Structure

```
src/
├── App.js              # Main React component
├── firebase.js         # Firebase configuration & messaging
├── index.js           # React app entry point
└── index.css          # Styles

public/
├── firebase-messaging-sw.js  # Service worker for background notifications
├── index.html         # HTML template
└── manifest.json      # PWA manifest
```

## 🔧 Troubleshooting

### Common Issues

1. **"No registration token available"**
   - Ensure app is running over HTTPS
   - Check VAPID key is correct
   - Verify Firebase config

2. **Notifications not showing**
   - Check browser notification permissions
   - Verify service worker is registered
   - Check browser console for errors

3. **Service worker not working**
   - Ensure `firebase-messaging-sw.js` is in `public/` folder
   - Check service worker registration in DevTools
   - Verify Firebase config in service worker

### Browser Support

- ✅ Chrome 50+
- ✅ Firefox 44+
- ✅ Safari 16+ (macOS 13+, iOS 16.4+)
- ✅ Edge 17+
- ❌ Internet Explorer (not supported)

## 📚 Additional Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Web Push Protocol](https://web.dev/push-notifications/)
- [Service Workers Guide](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 🎯 Next Steps

After setting up the app:

1. **Configure Firebase** - Update the config in `src/firebase.js` and `public/firebase-messaging-sw.js`
2. **Test Locally** - Run `npm start` and test on localhost
3. **Deploy to HTTPS** - Deploy to production for mobile testing
4. **Integrate Backend** - Use the provided examples to send notifications from your server
5. **Customize UI** - Modify styles and components to match your brand

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

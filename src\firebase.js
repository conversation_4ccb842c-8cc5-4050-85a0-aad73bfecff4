// src/firebase.js
import { initializeApp } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";

// TODO: Replace with your Firebase project configuration
// Get this from Firebase Console > Project Settings > General > Your Apps > Web
const firebaseConfig = {
  apiKey: "YOUR_API_KEY",
  authDomain: "YOUR_PROJECT.firebaseapp.com",
  projectId: "YOUR_PROJECT_ID",
  storageBucket: "YOUR_PROJECT.appspot.com",
  messagingSenderId: "YOUR_SENDER_ID",
  appId: "YOUR_APP_ID",
};

// TODO: Replace with your VAPID key
// Get this from Firebase Console > Project Settings > Cloud Messaging > Web Push certificates
const VAPID_KEY = "YOUR_PUBLIC_VAPID_KEY";

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const messaging = getMessaging(app);

/**
 * Request permission and get FCM registration token
 * This token is unique for each app installation and is used to send notifications
 */
export const requestForToken = async () => {
  try {
    // Request notification permission
    const permission = await Notification.requestPermission();
    
    if (permission === 'granted') {
      console.log('✅ Notification permission granted.');
      
      // Get registration token
      const token = await getToken(messaging, { vapidKey: VAPID_KEY });
      
      if (token) {
        console.log('✅ Current FCM token:', token);
        return {
          success: true,
          token: token,
          message: 'Token generated successfully!'
        };
      } else {
        console.log('❌ No registration token available');
        return {
          success: false,
          error: 'No registration token available. Make sure the app is running over HTTPS.'
        };
      }
    } else {
      console.log('❌ Notification permission denied');
      return {
        success: false,
        error: 'Notification permission denied. Please enable notifications in your browser settings.'
      };
    }
  } catch (error) {
    console.error('❌ Error getting token:', error);
    return {
      success: false,
      error: `Error getting token: ${error.message}`
    };
  }
};

/**
 * Listen for foreground messages
 * This handles notifications when the app is open and in focus
 */
export const onMessageListener = () =>
  new Promise((resolve) => {
    onMessage(messaging, (payload) => {
      console.log('📩 Message received in foreground:', payload);
      resolve(payload);
    });
  });

/**
 * Check if the browser supports notifications
 */
export const isNotificationSupported = () => {
  return 'Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window;
};

/**
 * Get current notification permission status
 */
export const getNotificationPermission = () => {
  if (!('Notification' in window)) {
    return 'not-supported';
  }
  return Notification.permission;
};

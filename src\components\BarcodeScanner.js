import React, { useState, useRef, useEffect } from 'react';
import QrScanner from 'qr-scanner';

const BarcodeScanner = ({ onScanResult, onError }) => {
  const [isScanning, setIsScanning] = useState(false);
  const [hasCamera, setHasCamera] = useState(false);
  const [cameras, setCameras] = useState([]);
  const [selectedCamera, setSelectedCamera] = useState('');
  const [scanResult, setScanResult] = useState('');
  const videoRef = useRef(null);
  const qrScannerRef = useRef(null);

  useEffect(() => {
    checkCameraAvailability();
    return () => {
      stopScanning();
    };
  }, []);

  const checkCameraAvailability = async () => {
    try {
      const availableCameras = await QrScanner.listCameras(true);
      setCameras(availableCameras);
      setHasCamera(availableCameras.length > 0);
      
      if (availableCameras.length > 0) {
        // Prefer back camera for mobile devices
        const backCamera = availableCameras.find(camera => 
          camera.label.toLowerCase().includes('back') || 
          camera.label.toLowerCase().includes('rear')
        );
        setSelectedCamera(backCamera ? backCamera.id : availableCameras[0].id);
      }
    } catch (error) {
      console.error('Error checking camera availability:', error);
      setHasCamera(false);
      if (onError) {
        onError('Camera access not available. Please ensure camera permissions are granted.');
      }
    }
  };

  const startScanning = async () => {
    if (!hasCamera || !videoRef.current) return;

    try {
      setIsScanning(true);
      
      qrScannerRef.current = new QrScanner(
        videoRef.current,
        (result) => {
          setScanResult(result.data);
          if (onScanResult) {
            onScanResult(result.data);
          }
          
          // Auto-stop scanning after successful scan
          setTimeout(() => {
            stopScanning();
          }, 1000);
        },
        {
          onDecodeError: (error) => {
            // Ignore decode errors (normal when no QR code is visible)
            console.debug('QR decode error:', error);
          },
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: selectedCamera
        }
      );

      await qrScannerRef.current.start();
      
    } catch (error) {
      console.error('Error starting scanner:', error);
      setIsScanning(false);
      if (onError) {
        onError(`Failed to start camera: ${error.message}`);
      }
    }
  };

  const stopScanning = () => {
    if (qrScannerRef.current) {
      qrScannerRef.current.stop();
      qrScannerRef.current.destroy();
      qrScannerRef.current = null;
    }
    setIsScanning(false);
  };

  const switchCamera = async (cameraId) => {
    setSelectedCamera(cameraId);
    if (isScanning) {
      stopScanning();
      setTimeout(() => {
        startScanning();
      }, 100);
    }
  };

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const result = await QrScanner.scanImage(file, {
        returnDetailedScanResult: true
      });
      
      setScanResult(result.data);
      if (onScanResult) {
        onScanResult(result.data);
      }
    } catch (error) {
      console.error('Error scanning image:', error);
      if (onError) {
        onError('No QR code found in the uploaded image.');
      }
    }
  };

  return (
    <div className="barcode-scanner">
      <div className="scanner-header">
        <h3>📷 QR Code Scanner</h3>
        <p>Scan QR codes to register devices or upload an image</p>
      </div>

      {!hasCamera ? (
        <div className="no-camera">
          <p>❌ No camera available or permission denied</p>
          <p>Please ensure camera permissions are granted and try again.</p>
        </div>
      ) : (
        <div className="scanner-container">
          <div className="video-container">
            <video
              ref={videoRef}
              className="scanner-video"
              style={{
                width: '100%',
                maxWidth: '400px',
                height: 'auto',
                border: '2px solid #ddd',
                borderRadius: '8px'
              }}
            />
            
            {isScanning && (
              <div className="scanning-overlay">
                <div className="scan-line"></div>
                <p>🔍 Scanning for QR codes...</p>
              </div>
            )}
          </div>

          <div className="scanner-controls">
            <button
              className="scanner-button primary"
              onClick={isScanning ? stopScanning : startScanning}
              disabled={!hasCamera}
            >
              {isScanning ? '⏹️ Stop Scanning' : '▶️ Start Scanning'}
            </button>

            {cameras.length > 1 && (
              <select
                value={selectedCamera}
                onChange={(e) => switchCamera(e.target.value)}
                className="camera-select"
                disabled={isScanning}
              >
                {cameras.map((camera) => (
                  <option key={camera.id} value={camera.id}>
                    {camera.label || `Camera ${camera.id}`}
                  </option>
                ))}
              </select>
            )}
          </div>

          <div className="file-upload">
            <label className="upload-label">
              📁 Upload QR Code Image
              <input
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="file-input"
              />
            </label>
          </div>
        </div>
      )}

      {scanResult && (
        <div className="scan-result">
          <h4>✅ Scan Result</h4>
          <div className="result-content">
            <p><strong>Data:</strong> {scanResult}</p>
            
            {scanResult.startsWith('http') && (
              <div className="result-actions">
                <button
                  className="result-button"
                  onClick={() => window.open(scanResult, '_blank')}
                >
                  🔗 Open Link
                </button>
                <button
                  className="result-button"
                  onClick={() => navigator.clipboard.writeText(scanResult)}
                >
                  📋 Copy
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="scanner-help">
        <h4>💡 Tips</h4>
        <ul>
          <li>Hold your device steady and ensure good lighting</li>
          <li>Position the QR code within the camera frame</li>
          <li>Try different distances if scanning fails</li>
          <li>Use the file upload option for saved QR code images</li>
        </ul>
      </div>
    </div>
  );
};

export default BarcodeScanner;

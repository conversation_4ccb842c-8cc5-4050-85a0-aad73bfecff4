# Backend Examples for FCM Push Notifications

This directory contains backend examples for sending push notifications to your React app.

## Node.js Example

### Setup

1. **Install dependencies:**
   ```bash
   cd backend-examples
   npm install
   ```

2. **Get Firebase Service Account Key:**
   - Go to Firebase Console > Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file as `serviceAccountKey.json` in this directory

3. **Start the server:**
   ```bash
   npm start
   # or for development with auto-reload:
   npm run dev
   ```

### API Endpoints

#### Register User Token
```bash
POST /api/register-token
Content-Type: application/json

{
  "userId": "user123",
  "token": "fcm-token-from-frontend"
}
```

#### Send Notification to User
```bash
POST /api/send-notification
Content-Type: application/json

{
  "userId": "user123",
  "title": "Hello!",
  "body": "This is a test notification",
  "data": {
    "customKey": "customValue"
  }
}
```

#### Broadcast to All Users
```bash
POST /api/broadcast
Content-Type: application/json

{
  "title": "Announcement",
  "body": "This goes to all registered users",
  "data": {
    "type": "broadcast"
  }
}
```

#### Get User Count
```bash
GET /api/users/count
```

### Example Usage

```javascript
// Register token from frontend
fetch('http://localhost:3001/api/register-token', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: 'user123',
    token: 'your-fcm-token-here'
  })
});

// Send notification
fetch('http://localhost:3001/api/send-notification', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: 'user123',
    title: 'Hello from Backend!',
    body: 'This is a test notification 🚀'
  })
});
```

## Other Examples

### cURL
```bash
# Send notification using cURL
curl -X POST "https://fcm.googleapis.com/fcm/send" \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
        "to": "USER_FCM_TOKEN",
        "notification": {
          "title": "Hello from cURL",
          "body": "This is a test notification 🚀"
        }
      }'
```

### Python
```python
import requests
import json

def send_notification(server_key, token, title, body):
    url = "https://fcm.googleapis.com/fcm/send"
    headers = {
        "Authorization": f"key={server_key}",
        "Content-Type": "application/json"
    }
    payload = {
        "to": token,
        "notification": {
            "title": title,
            "body": body
        }
    }
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    return response.json()
```

## Security Notes

- Never expose your Firebase Server Key or Service Account Key in client-side code
- Use environment variables for sensitive configuration
- Implement proper authentication and authorization
- Validate and sanitize all input data
- Rate limit your notification endpoints

## Production Considerations

- Use a proper database instead of in-memory storage
- Implement user authentication
- Add logging and monitoring
- Handle token refresh and cleanup
- Implement notification scheduling
- Add analytics and delivery tracking

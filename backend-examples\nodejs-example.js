// backend-examples/nodejs-example.js
// Example Node.js backend for sending FCM push notifications

const admin = require('firebase-admin');
const express = require('express');
const cors = require('cors');

// Initialize Express app
const app = express();
app.use(cors());
app.use(express.json());

// Initialize Firebase Admin SDK
// Download your service account key from Firebase Console > Project Settings > Service Accounts
const serviceAccount = require('./serviceAccountKey.json'); // You need to download this

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// Store user tokens (in production, use a database)
const userTokens = new Map();

/**
 * Register a user's FCM token
 */
app.post('/api/register-token', (req, res) => {
  const { userId, token } = req.body;
  
  if (!userId || !token) {
    return res.status(400).json({ error: 'userId and token are required' });
  }
  
  userTokens.set(userId, token);
  console.log(`✅ Registered token for user ${userId}`);
  
  res.json({ success: true, message: 'Token registered successfully' });
});

/**
 * Send notification to a specific user
 */
app.post('/api/send-notification', async (req, res) => {
  const { userId, title, body, data = {} } = req.body;
  
  if (!userId || !title || !body) {
    return res.status(400).json({ error: 'userId, title, and body are required' });
  }
  
  const token = userTokens.get(userId);
  if (!token) {
    return res.status(404).json({ error: 'User token not found' });
  }
  
  try {
    const result = await sendNotification(token, title, body, data);
    res.json({ success: true, messageId: result });
  } catch (error) {
    console.error('❌ Error sending notification:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Send notification to all registered users
 */
app.post('/api/broadcast', async (req, res) => {
  const { title, body, data = {} } = req.body;
  
  if (!title || !body) {
    return res.status(400).json({ error: 'title and body are required' });
  }
  
  const tokens = Array.from(userTokens.values());
  if (tokens.length === 0) {
    return res.status(400).json({ error: 'No registered tokens found' });
  }
  
  try {
    const results = await sendToMultipleTokens(tokens, title, body, data);
    res.json({ 
      success: true, 
      totalSent: results.successCount,
      totalFailed: results.failureCount,
      results: results.responses 
    });
  } catch (error) {
    console.error('❌ Error broadcasting:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Get registered users count
 */
app.get('/api/users/count', (req, res) => {
  res.json({ count: userTokens.size });
});

/**
 * Send notification to a single token
 */
async function sendNotification(token, title, body, data = {}) {
  const message = {
    notification: {
      title: title,
      body: body
    },
    data: {
      ...data,
      timestamp: new Date().toISOString()
    },
    token: token,
    webpush: {
      notification: {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        requireInteraction: false,
        actions: [
          {
            action: 'open',
            title: 'Open App'
          }
        ]
      }
    }
  };

  try {
    const response = await admin.messaging().send(message);
    console.log('✅ Successfully sent message:', response);
    return response;
  } catch (error) {
    console.error('❌ Error sending message:', error);
    
    // Handle invalid tokens
    if (error.code === 'messaging/registration-token-not-registered') {
      console.log('🗑️ Removing invalid token:', token);
      // Remove invalid token from storage
      for (const [userId, userToken] of userTokens.entries()) {
        if (userToken === token) {
          userTokens.delete(userId);
          break;
        }
      }
    }
    
    throw error;
  }
}

/**
 * Send notification to multiple tokens
 */
async function sendToMultipleTokens(tokens, title, body, data = {}) {
  const message = {
    notification: {
      title: title,
      body: body
    },
    data: {
      ...data,
      timestamp: new Date().toISOString()
    },
    tokens: tokens,
    webpush: {
      notification: {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        requireInteraction: false
      }
    }
  };

  try {
    const response = await admin.messaging().sendMulticast(message);
    console.log(`✅ Successfully sent ${response.successCount} messages`);
    
    // Handle failed tokens
    if (response.failureCount > 0) {
      response.responses.forEach((resp, idx) => {
        if (!resp.success) {
          console.error(`❌ Failed to send to token ${idx}:`, resp.error);
          
          // Remove invalid tokens
          if (resp.error?.code === 'messaging/registration-token-not-registered') {
            const invalidToken = tokens[idx];
            for (const [userId, userToken] of userTokens.entries()) {
              if (userToken === invalidToken) {
                userTokens.delete(userId);
                console.log(`🗑️ Removed invalid token for user ${userId}`);
                break;
              }
            }
          }
        }
      });
    }
    
    return response;
  } catch (error) {
    console.error('❌ Error sending multicast:', error);
    throw error;
  }
}

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'FCM Backend is running!', 
    timestamp: new Date().toISOString(),
    registeredUsers: userTokens.size
  });
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`🚀 FCM Backend server running on port ${PORT}`);
  console.log(`📡 Test endpoint: http://localhost:${PORT}/api/test`);
});

module.exports = app;

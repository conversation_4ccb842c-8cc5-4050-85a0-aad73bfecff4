// public/firebase-messaging-sw.js
// This service worker handles background notifications when the app is closed

// Import Firebase scripts for service worker
importScripts("https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.23.0/firebase-messaging-compat.js");

// Firebase project configuration - matches src/firebase.js
const firebaseConfig = {
  apiKey: "AIzaSyBCsfQqgeryVdZjRGzdEzF4q-qyqcOaaMQ",
  authDomain: "push-demo-ff79a.firebaseapp.com",
  projectId: "push-demo-ff79a",
  storageBucket: "push-demo-ff79a.firebasestorage.app",
  messagingSenderId: "224185107862",
  appId: "1:224185107862:web:b0c42a907ff74b77ba89b7",
  measurementId: "G-6HTKKRB8JX"
};

// Initialize Firebase in service worker
firebase.initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging and get a reference to the service
const messaging = firebase.messaging();

/**
 * Handle background messages
 * This function is called when a notification is received while the app is in the background
 */
messaging.onBackgroundMessage(function(payload) {
  console.log('📩 Background Message received:', payload);
  
  // Extract notification data
  const notificationTitle = payload.notification?.title || 'New Notification';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new message',
    icon: payload.notification?.icon || '/favicon.ico',
    badge: '/favicon.ico',
    tag: payload.notification?.tag || 'default',
    data: payload.data || {},
    actions: [
      {
        action: 'open',
        title: 'Open App'
      },
      {
        action: 'close',
        title: 'Close'
      }
    ],
    requireInteraction: false,
    silent: false
  };

  // Show the notification
  self.registration.showNotification(notificationTitle, notificationOptions);
});

/**
 * Handle notification click events
 */
self.addEventListener('notificationclick', function(event) {
  console.log('🔔 Notification clicked:', event);
  
  // Close the notification
  event.notification.close();
  
  // Handle different actions
  if (event.action === 'close') {
    // Just close the notification
    return;
  }
  
  // Default action or 'open' action - open the app
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then(function(clientList) {
      // Check if the app is already open
      for (let i = 0; i < clientList.length; i++) {
        const client = clientList[i];
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          // Focus the existing window
          return client.focus();
        }
      }
      
      // If no window is open, open a new one
      if (clients.openWindow) {
        return clients.openWindow('/');
      }
    })
  );
});

/**
 * Handle notification close events
 */
self.addEventListener('notificationclose', function(event) {
  console.log('🔕 Notification closed:', event);
  // You can track notification dismissals here if needed
});

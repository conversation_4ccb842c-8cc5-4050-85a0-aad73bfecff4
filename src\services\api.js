// API service for handling FCM token registration and device management

const API_BASE_URL = process.env.REACT_APP_API_URL || '/api';

/**
 * Register a device with FCM token
 */
export const registerDevice = async (deviceData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/register-device`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionId: deviceData.sessionId,
        fcmToken: deviceData.token,
        deviceInfo: deviceData.deviceInfo,
        timestamp: deviceData.timestamp,
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return { success: true, data: result };
    
  } catch (error) {
    console.error('❌ Device registration failed:', error);
    return { 
      success: false, 
      error: error.message || 'Failed to register device' 
    };
  }
};

/**
 * Send a test notification to a specific token
 */
export const sendTestNotification = async (token, title = 'Test Notification', body = 'This is a test notification! 🚀') => {
  try {
    const response = await fetch(`${API_BASE_URL}/send-test-notification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: token,
        title: title,
        body: body,
        data: {
          type: 'test',
          timestamp: new Date().toISOString()
        }
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return { success: true, data: result };
    
  } catch (error) {
    console.error('❌ Test notification failed:', error);
    return { 
      success: false, 
      error: error.message || 'Failed to send test notification' 
    };
  }
};

/**
 * Get list of registered devices
 */
export const getRegisteredDevices = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/devices`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return { success: true, data: result };
    
  } catch (error) {
    console.error('❌ Failed to get devices:', error);
    return { 
      success: false, 
      error: error.message || 'Failed to get registered devices' 
    };
  }
};

/**
 * Send notification to all registered devices
 */
export const broadcastNotification = async (title, body, data = {}) => {
  try {
    const response = await fetch(`${API_BASE_URL}/broadcast`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title: title,
        body: body,
        data: {
          ...data,
          timestamp: new Date().toISOString()
        }
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return { success: true, data: result };
    
  } catch (error) {
    console.error('❌ Broadcast failed:', error);
    return { 
      success: false, 
      error: error.message || 'Failed to broadcast notification' 
    };
  }
};

/**
 * Remove a device registration
 */
export const unregisterDevice = async (token) => {
  try {
    const response = await fetch(`${API_BASE_URL}/unregister-device`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: token
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return { success: true, data: result };
    
  } catch (error) {
    console.error('❌ Device unregistration failed:', error);
    return { 
      success: false, 
      error: error.message || 'Failed to unregister device' 
    };
  }
};

/**
 * Get server status and statistics
 */
export const getServerStatus = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return { success: true, data: result };
    
  } catch (error) {
    console.error('❌ Failed to get server status:', error);
    return { 
      success: false, 
      error: error.message || 'Failed to get server status' 
    };
  }
};

/**
 * Utility function to check if API is available
 */
export const checkAPIConnection = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    return response.ok;
  } catch (error) {
    console.error('❌ API connection check failed:', error);
    return false;
  }
};

/**
 * Generate a QR code data URL with registration information
 */
export const generateQRCodeData = (sessionId, baseUrl = window.location.origin) => {
  const registrationUrl = `${baseUrl}/register?session=${sessionId}`;
  return {
    url: registrationUrl,
    sessionId: sessionId,
    timestamp: new Date().toISOString()
  };
};

/**
 * Validate FCM token format
 */
export const validateFCMToken = (token) => {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  // FCM tokens are typically 152+ characters long and contain specific patterns
  return token.length > 100 && token.includes(':');
};

export default {
  registerDevice,
  sendTestNotification,
  getRegisteredDevices,
  broadcastNotification,
  unregisterDevice,
  getServerStatus,
  checkAPIConnection,
  generateQRCodeData,
  validateFCMToken
};

// server.js - Main server file for Heroku deployment
const express = require('express');
const cors = require('cors');
const path = require('path');
const admin = require('firebase-admin');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Serve static files from React build
app.use(express.static(path.join(__dirname, 'build')));

// Initialize Firebase Admin SDK
// Note: You'll need to set FIREBASE_SERVICE_ACCOUNT_KEY environment variable
let firebaseInitialized = false;

try {
  if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
    const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
    firebaseInitialized = true;
    console.log('✅ Firebase Admin SDK initialized');
  } else {
    console.log('⚠️ Firebase service account key not found. Some features may not work.');
  }
} catch (error) {
  console.error('❌ Error initializing Firebase Admin SDK:', error);
}

// Store user tokens (in production, use a database)
const userTokens = new Map();

// API Routes

/**
 * Health check endpoint
 */
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    firebase: firebaseInitialized 
  });
});

/**
 * Server status endpoint
 */
app.get('/api/status', (req, res) => {
  res.json({ 
    status: 'running',
    uptime: process.uptime(),
    registeredDevices: userTokens.size,
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    firebase: firebaseInitialized
  });
});

/**
 * Register a device with FCM token (QR code flow)
 */
app.post('/api/register-device', (req, res) => {
  const { sessionId, fcmToken, deviceInfo, timestamp } = req.body;
  
  if (!sessionId || !fcmToken) {
    return res.status(400).json({ error: 'sessionId and fcmToken are required' });
  }
  
  // Store device registration
  const deviceId = `device_${Date.now()}`;
  userTokens.set(deviceId, {
    token: fcmToken,
    sessionId: sessionId,
    deviceInfo: deviceInfo,
    registeredAt: timestamp || new Date().toISOString(),
    lastSeen: new Date().toISOString()
  });
  
  console.log(`✅ Registered device ${deviceId} via QR code (session: ${sessionId})`);
  
  res.json({ 
    success: true, 
    message: 'Device registered successfully',
    deviceId: deviceId
  });
});

/**
 * Get registered devices
 */
app.get('/api/devices', (req, res) => {
  const devices = Array.from(userTokens.entries()).map(([id, record]) => ({
    id: id,
    token: record.token ? record.token.slice(0, 20) + '...' : record.slice(0, 20) + '...',
    registeredAt: record.registeredAt || 'Unknown',
    lastSeen: record.lastSeen || 'Unknown',
    deviceInfo: record.deviceInfo || null,
    sessionId: record.sessionId || null
  }));
  
  res.json({ 
    devices: devices,
    count: devices.length 
  });
});

/**
 * Send test notification to a specific token
 */
app.post('/api/send-test-notification', async (req, res) => {
  if (!firebaseInitialized) {
    return res.status(500).json({ error: 'Firebase Admin SDK not initialized' });
  }

  const { token, title, body, data = {} } = req.body;
  
  if (!token || !title || !body) {
    return res.status(400).json({ error: 'token, title, and body are required' });
  }
  
  try {
    const result = await sendNotification(token, title, body, data);
    res.json({ success: true, messageId: result });
  } catch (error) {
    console.error('❌ Error sending test notification:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Send notification to all registered users
 */
app.post('/api/broadcast', async (req, res) => {
  if (!firebaseInitialized) {
    return res.status(500).json({ error: 'Firebase Admin SDK not initialized' });
  }

  const { title, body, data = {} } = req.body;
  
  if (!title || !body) {
    return res.status(400).json({ error: 'title and body are required' });
  }
  
  const tokens = Array.from(userTokens.values()).map(record => 
    record.token || record
  );
  
  if (tokens.length === 0) {
    return res.status(400).json({ error: 'No registered tokens found' });
  }
  
  try {
    const results = await sendToMultipleTokens(tokens, title, body, data);
    res.json({ 
      success: true, 
      totalSent: results.successCount,
      totalFailed: results.failureCount,
      results: results.responses 
    });
  } catch (error) {
    console.error('❌ Error broadcasting:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Send notification to a single token
 */
async function sendNotification(token, title, body, data = {}) {
  const message = {
    notification: {
      title: title,
      body: body
    },
    data: {
      ...data,
      timestamp: new Date().toISOString()
    },
    token: token,
    webpush: {
      notification: {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        requireInteraction: false,
        actions: [
          {
            action: 'open',
            title: 'Open App'
          }
        ]
      }
    }
  };

  try {
    const response = await admin.messaging().send(message);
    console.log('✅ Successfully sent message:', response);
    return response;
  } catch (error) {
    console.error('❌ Error sending message:', error);
    
    // Handle invalid tokens
    if (error.code === 'messaging/registration-token-not-registered') {
      console.log('🗑️ Removing invalid token:', token);
      // Remove invalid token from storage
      for (const [userId, userToken] of userTokens.entries()) {
        if ((userToken.token || userToken) === token) {
          userTokens.delete(userId);
          break;
        }
      }
    }
    
    throw error;
  }
}

/**
 * Send notification to multiple tokens
 */
async function sendToMultipleTokens(tokens, title, body, data = {}) {
  const message = {
    notification: {
      title: title,
      body: body
    },
    data: {
      ...data,
      timestamp: new Date().toISOString()
    },
    tokens: tokens,
    webpush: {
      notification: {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        requireInteraction: false
      }
    }
  };

  try {
    const response = await admin.messaging().sendMulticast(message);
    console.log(`✅ Successfully sent ${response.successCount} messages`);
    
    // Handle failed tokens
    if (response.failureCount > 0) {
      response.responses.forEach((resp, idx) => {
        if (!resp.success) {
          console.error(`❌ Failed to send to token ${idx}:`, resp.error);
          
          // Remove invalid tokens
          if (resp.error?.code === 'messaging/registration-token-not-registered') {
            const invalidToken = tokens[idx];
            for (const [userId, userToken] of userTokens.entries()) {
              if ((userToken.token || userToken) === invalidToken) {
                userTokens.delete(userId);
                console.log(`🗑️ Removed invalid token for user ${userId}`);
                break;
              }
            }
          }
        }
      });
    }
    
    return response;
  } catch (error) {
    console.error('❌ Error sending multicast:', error);
    throw error;
  }
}

// Serve React app for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📡 API endpoints available at /api/*`);
  console.log(`🔥 Firebase Admin SDK: ${firebaseInitialized ? 'Initialized' : 'Not initialized'}`);
});

import React, { useState, useEffect } from 'react';
import { 
  requestForToken, 
  onMessageListener, 
  isNotificationSupported, 
  getNotificationPermission 
} from './firebase';

function App() {
  const [token, setToken] = useState('');
  const [status, setStatus] = useState({ type: '', message: '' });
  const [isLoading, setIsLoading] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [permission, setPermission] = useState(getNotificationPermission());

  // Listen for foreground messages
  useEffect(() => {
    if (isNotificationSupported()) {
      onMessageListener().then(payload => {
        console.log('📩 Foreground notification received:', payload);
        
        // Add to notifications list
        const newNotification = {
          id: Date.now(),
          title: payload.notification?.title || 'New Message',
          body: payload.notification?.body || 'You have a new notification',
          timestamp: new Date().toLocaleTimeString(),
          data: payload.data || {}
        };
        
        setNotifications(prev => [newNotification, ...prev]);
        
        // Show browser notification if permission is granted
        if (Notification.permission === 'granted') {
          new Notification(newNotification.title, {
            body: newNotification.body,
            icon: '/favicon.ico'
          });
        }
        
        setStatus({
          type: 'info',
          message: `New notification: ${newNotification.title}`
        });
      }).catch(err => {
        console.error('Error setting up message listener:', err);
      });
    }
  }, []);

  // Update permission status
  useEffect(() => {
    const checkPermission = () => {
      setPermission(getNotificationPermission());
    };
    
    // Check permission on focus
    window.addEventListener('focus', checkPermission);
    return () => window.removeEventListener('focus', checkPermission);
  }, []);

  const handleEnableNotifications = async () => {
    if (!isNotificationSupported()) {
      setStatus({
        type: 'error',
        message: 'Push notifications are not supported in this browser.'
      });
      return;
    }

    setIsLoading(true);
    setStatus({ type: '', message: '' });

    try {
      const result = await requestForToken();
      
      if (result.success) {
        setToken(result.token);
        setPermission('granted');
        setStatus({
          type: 'success',
          message: result.message + ' You can now receive push notifications!'
        });
        
        // Send token to your backend here
        console.log('🚀 Send this token to your backend:', result.token);
        
      } else {
        setStatus({
          type: 'error',
          message: result.error
        });
      }
    } catch (error) {
      setStatus({
        type: 'error',
        message: `Unexpected error: ${error.message}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyTokenToClipboard = () => {
    navigator.clipboard.writeText(token).then(() => {
      setStatus({
        type: 'success',
        message: 'Token copied to clipboard!'
      });
    }).catch(() => {
      setStatus({
        type: 'error',
        message: 'Failed to copy token to clipboard'
      });
    });
  };

  const clearNotifications = () => {
    setNotifications([]);
    setStatus({
      type: 'info',
      message: 'Notifications cleared'
    });
  };

  const getPermissionStatus = () => {
    switch (permission) {
      case 'granted':
        return { text: '✅ Granted', color: '#28a745' };
      case 'denied':
        return { text: '❌ Denied', color: '#dc3545' };
      case 'default':
        return { text: '⏳ Not requested', color: '#ffc107' };
      case 'not-supported':
        return { text: '❌ Not supported', color: '#6c757d' };
      default:
        return { text: '❓ Unknown', color: '#6c757d' };
    }
  };

  const permissionStatus = getPermissionStatus();

  return (
    <div className="app-container">
      <div className="card">
        <h1 className="title">🔔 React Push Notifications</h1>
        
        <div style={{ marginBottom: '20px' }}>
          <p><strong>Browser Support:</strong> {isNotificationSupported() ? '✅ Supported' : '❌ Not Supported'}</p>
          <p><strong>Permission:</strong> <span style={{ color: permissionStatus.color }}>{permissionStatus.text}</span></p>
        </div>

        {status.message && (
          <div className={`status ${status.type}`}>
            {status.message}
          </div>
        )}

        <div>
          <button 
            className="notification-button"
            onClick={handleEnableNotifications}
            disabled={isLoading || !isNotificationSupported()}
          >
            {isLoading ? '⏳ Requesting...' : '🔔 Enable Notifications'}
          </button>
        </div>

        {token && (
          <div>
            <h3>FCM Token:</h3>
            <div className="token-display">
              {token}
            </div>
            <button 
              className="notification-button"
              onClick={copyTokenToClipboard}
              style={{ fontSize: '14px', padding: '10px 20px' }}
            >
              📋 Copy Token
            </button>
          </div>
        )}

        {notifications.length > 0 && (
          <div className="notification-log">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h3>Recent Notifications ({notifications.length})</h3>
              <button 
                className="notification-button"
                onClick={clearNotifications}
                style={{ fontSize: '12px', padding: '5px 10px' }}
              >
                Clear
              </button>
            </div>
            {notifications.slice(0, 5).map(notification => (
              <div key={notification.id} className="notification-item">
                <h4>{notification.title}</h4>
                <p>{notification.body}</p>
                <small style={{ color: '#999' }}>{notification.timestamp}</small>
              </div>
            ))}
          </div>
        )}

        {!token && permission === 'granted' && (
          <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
            <p>💡 <strong>Tip:</strong> If you've already granted permission but don't see a token, try refreshing the page.</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;

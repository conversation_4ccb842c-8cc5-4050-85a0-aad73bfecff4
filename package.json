{"name": "react-push-notifications", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "ajv": "^8.17.1", "cors": "^2.8.5", "express": "^4.18.2", "firebase": "^9.23.0", "firebase-admin": "^11.10.1", "qrcode": "^1.5.3", "qr-scanner": "^1.4.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-qr-code": "^2.0.12", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "uuid": "^9.0.0", "web-vitals": "^2.1.4"}, "scripts": {"dev": "react-scripts start", "start": "node server.js", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "heroku-postbuild": "npm run build"}, "engines": {"node": "18.x", "npm": "9.x"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
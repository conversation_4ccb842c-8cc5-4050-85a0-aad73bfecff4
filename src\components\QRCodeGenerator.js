import React, { useState, useEffect } from 'react';
import QRCode from 'react-qr-code';
import { v4 as uuidv4 } from 'uuid';

const QRCodeGenerator = ({ onTokenReceived }) => {
  const [qrData, setQrData] = useState('');
  const [sessionId, setSessionId] = useState('');
  const [registeredTokens, setRegisteredTokens] = useState([]);
  const [isListening, setIsListening] = useState(false);

  useEffect(() => {
    generateNewQRCode();
  }, []);

  const generateNewQRCode = () => {
    const newSessionId = uuidv4();
    setSessionId(newSessionId);
    
    // Create the registration URL that will be embedded in QR code
    const baseUrl = window.location.origin;
    const registrationUrl = `${baseUrl}/register?session=${newSessionId}`;
    
    setQrData(registrationUrl);
    setIsListening(true);
    
    // Start listening for token registration
    startTokenListener(newSessionId);
  };

  const startTokenListener = (sessionId) => {
    // In a real app, you'd use WebSocket or polling to listen for token registration
    // For demo purposes, we'll simulate this with localStorage polling
    const checkForToken = () => {
      const storedToken = localStorage.getItem(`fcm_token_${sessionId}`);
      if (storedToken) {
        const tokenData = JSON.parse(storedToken);
        setRegisteredTokens(prev => [...prev, tokenData]);
        setIsListening(false);
        
        // Call the callback with the new token
        if (onTokenReceived) {
          onTokenReceived(tokenData);
        }
        
        // Clean up
        localStorage.removeItem(`fcm_token_${sessionId}`);
        
        // Send to API
        sendTokenToAPI(tokenData);
        
        return;
      }
      
      // Continue polling if still listening
      if (isListening) {
        setTimeout(checkForToken, 2000);
      }
    };
    
    setTimeout(checkForToken, 2000);
  };

  const sendTokenToAPI = async (tokenData) => {
    try {
      // Replace with your actual API endpoint
      const response = await fetch('/api/register-device', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: tokenData.sessionId,
          fcmToken: tokenData.token,
          deviceInfo: tokenData.deviceInfo,
          timestamp: tokenData.timestamp
        })
      });
      
      if (response.ok) {
        console.log('✅ Token sent to API successfully');
      } else {
        console.error('❌ Failed to send token to API');
      }
    } catch (error) {
      console.error('❌ Error sending token to API:', error);
    }
  };

  const copyQRUrl = () => {
    navigator.clipboard.writeText(qrData).then(() => {
      alert('QR URL copied to clipboard!');
    });
  };

  const downloadQRCode = () => {
    const svg = document.getElementById('qr-code-svg');
    const svgData = new XMLSerializer().serializeToString(svg);
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      
      const pngFile = canvas.toDataURL('image/png');
      const downloadLink = document.createElement('a');
      downloadLink.download = `qr-code-${sessionId.slice(0, 8)}.png`;
      downloadLink.href = pngFile;
      downloadLink.click();
    };
    
    img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
  };

  return (
    <div className="qr-generator-container">
      <div className="qr-section">
        <h3>📱 Device Registration QR Code</h3>
        <p>Scan this QR code with your phone camera to automatically register for push notifications</p>
        
        <div className="qr-code-wrapper">
          {qrData && (
            <QRCode
              id="qr-code-svg"
              value={qrData}
              size={256}
              style={{ height: "auto", maxWidth: "100%", width: "100%" }}
              viewBox={`0 0 256 256`}
            />
          )}
        </div>
        
        <div className="qr-actions">
          <button className="qr-button" onClick={copyQRUrl}>
            📋 Copy URL
          </button>
          <button className="qr-button" onClick={downloadQRCode}>
            💾 Download QR
          </button>
          <button className="qr-button" onClick={generateNewQRCode}>
            🔄 New QR Code
          </button>
        </div>
        
        <div className="qr-info">
          <p><strong>Session ID:</strong> {sessionId.slice(0, 8)}...</p>
          <p><strong>Status:</strong> 
            {isListening ? (
              <span style={{ color: '#007bff' }}> 🔄 Waiting for scan...</span>
            ) : (
              <span style={{ color: '#28a745' }}> ✅ Ready</span>
            )}
          </p>
        </div>
      </div>
      
      {registeredTokens.length > 0 && (
        <div className="registered-tokens">
          <h4>📲 Registered Devices ({registeredTokens.length})</h4>
          {registeredTokens.map((token, index) => (
            <div key={index} className="token-item">
              <div className="token-info">
                <p><strong>Device:</strong> {token.deviceInfo?.userAgent || 'Unknown'}</p>
                <p><strong>Time:</strong> {new Date(token.timestamp).toLocaleString()}</p>
                <p><strong>Token:</strong> {token.token.slice(0, 20)}...</p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default QRCodeGenerator;

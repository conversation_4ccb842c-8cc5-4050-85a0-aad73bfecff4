# 🚀 Heroku Deployment Guide

## Prerequisites

1. **Heroku Account** - Sign up at [heroku.com](https://heroku.com)
2. **Heroku CLI** - Install from [devcenter.heroku.com/articles/heroku-cli](https://devcenter.heroku.com/articles/heroku-cli)
3. **Git** - Make sure Git is installed
4. **Firebase Service Account Key** - Download from Firebase Console

## Step-by-Step Deployment

### 1. Prepare Firebase Service Account

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: **push-demo-ff79a**
3. Go to **Project Settings** → **Service Accounts**
4. Click **Generate new private key**
5. Download the JSON file
6. **Copy the entire JSON content** (you'll need this for <PERSON>ku)

### 2. Initialize Git Repository

```bash
# Initialize git (if not already done)
git init

# Add all files
git add .

# Commit
git commit -m "Initial commit for Heroku deployment"
```

### 3. Create Heroku App

```bash
# Login to Heroku
heroku login

# Create a new Heroku app (replace 'your-app-name' with your desired name)
heroku create your-push-notification-app

# Or if you want Heroku to generate a name:
heroku create
```

### 4. Set Environment Variables

```bash
# Set the Firebase service account key (replace with your actual JSON)
heroku config:set FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account","project_id":"push-demo-ff79a","private_key_id":"***","private_key":"***","client_email":"***","client_id":"***","auth_uri":"***","token_uri":"***","auth_provider_x509_cert_url":"***","client_x509_cert_url":"***"}'

# Optional: Disable source maps for production
heroku config:set GENERATE_SOURCEMAP=false

# Optional: Optimize runtime chunk
heroku config:set INLINE_RUNTIME_CHUNK=false
```

**Important:** Make sure to replace the JSON with your actual Firebase service account key!

### 5. Deploy to Heroku

```bash
# Deploy to Heroku
git push heroku main

# Or if your main branch is called 'master':
git push heroku master
```

### 6. Open Your App

```bash
# Open your deployed app
heroku open

# Or check logs if there are issues
heroku logs --tail
```

## 🔧 Configuration Details

### What Happens During Deployment:

1. **Build Process**: Heroku runs `npm run build` to create optimized React files
2. **Server Start**: Heroku starts `node server.js` which serves both API and React app
3. **Environment**: Firebase Admin SDK initializes with your service account key

### App Structure on Heroku:

```
your-app.herokuapp.com/
├── /                    # React app (all routes)
├── /register           # QR code registration page
├── /api/health         # API health check
├── /api/status         # Server status
├── /api/register-device # Device registration
├── /api/devices        # List registered devices
├── /api/send-test-notification # Send test notification
└── /api/broadcast      # Broadcast to all devices
```

## 🧪 Testing Your Deployment

### 1. Test the App
- Visit your Heroku URL
- Generate QR codes
- Test device registration
- Check notifications tab

### 2. Test API Endpoints
```bash
# Check API health
curl https://your-app.herokuapp.com/api/health

# Check server status
curl https://your-app.herokuapp.com/api/status
```

### 3. Test QR Code Flow
1. Generate QR code in your app
2. Scan with phone camera
3. Register device
4. Send test notification

## 🔍 Troubleshooting

### Common Issues:

1. **Build Fails**
   ```bash
   # Check build logs
   heroku logs --tail
   
   # Try building locally first
   npm run build
   ```

2. **Firebase Not Working**
   ```bash
   # Check if service account key is set
   heroku config:get FIREBASE_SERVICE_ACCOUNT_KEY
   
   # Make sure the JSON is valid
   ```

3. **App Won't Start**
   ```bash
   # Check server logs
   heroku logs --tail
   
   # Restart the app
   heroku restart
   ```

### Useful Heroku Commands:

```bash
# View logs
heroku logs --tail

# Restart app
heroku restart

# Check config vars
heroku config

# Open app in browser
heroku open

# Run commands on Heroku
heroku run bash
```

## 🔄 Updating Your App

When you make changes:

```bash
# Add changes
git add .

# Commit
git commit -m "Update app"

# Deploy
git push heroku main
```

## 🌐 Custom Domain (Optional)

To use a custom domain:

```bash
# Add custom domain
heroku domains:add yourdomain.com

# Configure DNS to point to Heroku
# Add CNAME record: yourdomain.com → your-app.herokuapp.com
```

## 📊 Monitoring

- **Heroku Dashboard**: Monitor app performance
- **Logs**: `heroku logs --tail`
- **Metrics**: Available in Heroku dashboard

Your app will be available at: `https://your-app-name.herokuapp.com`

#!/bin/bash

# Heroku Deployment Script for React Push Notifications App

echo "🚀 Starting Heroku deployment process..."

# Check if Heroku CLI is installed
if ! command -v heroku &> /dev/null; then
    echo "❌ Heroku CLI is not installed. Please install it first:"
    echo "   https://devcenter.heroku.com/articles/heroku-cli"
    exit 1
fi

# Check if user is logged in to Heroku
if ! heroku auth:whoami &> /dev/null; then
    echo "🔐 Please login to Hero<PERSON> first:"
    heroku login
fi

# Initialize git if not already done
if [ ! -d ".git" ]; then
    echo "📁 Initializing Git repository..."
    git init
    git add .
    git commit -m "Initial commit for Heroku deployment"
fi

# Create Heroku app
echo "🏗️ Creating Heroku app..."
read -p "Enter your app name (or press Enter for auto-generated): " APP_NAME

if [ -z "$APP_NAME" ]; then
    heroku create
else
    heroku create $APP_NAME
fi

# Set environment variables
echo "🔧 Setting up environment variables..."
echo "⚠️  You need to set your Firebase service account key manually:"
echo "   heroku config:set FIREBASE_SERVICE_ACCOUNT_KEY='your-json-here'"
echo ""
echo "📋 Copy your Firebase service account JSON and run the above command"

# Set other environment variables
heroku config:set GENERATE_SOURCEMAP=false
heroku config:set INLINE_RUNTIME_CHUNK=false

# Deploy to Heroku
echo "🚀 Deploying to Heroku..."
git push heroku main

echo "✅ Deployment complete!"
echo "🌐 Opening your app..."
heroku open

echo ""
echo "📊 To view logs: heroku logs --tail"
echo "🔄 To redeploy: git push heroku main"
